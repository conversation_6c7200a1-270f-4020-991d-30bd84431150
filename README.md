🌐️ English | [中文](doc/zh-cn/readme.md)

<p align="center">
  <img src="doc/asset/image/head.png" width=800/>
</p>


[![Star](https://shields.io/github/stars/tugraph-family/chat2graph?logo=startrek&label=Star&color=yellow)](https://github.com/TuGraph-family/chat2graph/stargazers)
[![Fork](https://shields.io/github/forks/tugraph-family/chat2graph?logo=forgejo&label=Fork&color=orange)](https://github.com/TuGraph-family/chat2graph/forks)
[![Contributor](https://shields.io/github/contributors/tugraph-family/chat2graph?logo=actigraph&label=Contributor&color=abcdef)](https://github.com/TuGraph-family/chat2graph/contributors)
[![Commit](https://badgen.net/github/last-commit/tugraph-family/chat2graph/master?icon=git&label=Commit)](https://github.com/TuGraph-family/chat2graph/commits/master)
[![License](https://shields.io/github/license/tugraph-family/chat2graph?logo=apache&label=License&color=blue)](https://www.apache.org/licenses/LICENSE-2.0.html)
[![Release](https://shields.io/github/v/release/tugraph-family/chat2graph.svg?logo=stackblitz&label=Version&color=red)](https://github.com/TuGraph-family/chat2graph/releases)

[Chat2Graph](https://chat2graph.vercel.app) is a **Graph Native Agentic System**.

<video controls src="https://github.com/user-attachments/assets/7c859d37-cd1e-431f-8e81-8459bc605879" style="max-width: 100%;">
  Your browser does not support the video tag.
</video>

Using agents as a blueprint, and exploring the technological innovation of "Graph + AI" is our value proposition.

## Documentation

* [Introduction](doc/en-us/introduction.md): Learn about the technical background of Chat2Graph.
* [Quickstart](doc/en-us/quickstart.md): Install and experience Chat2Graph from source code.
* [Principle](doc/en-us/principle/overview.md): Introduction to Chat2Graph's architecture design and implementation principles.
* [Cookbook](doc/en-us/cookbook/overview.md): Introduction to Chat2Graph's features and operations.
* [Development](doc/en-us/development/overview.md): Chat2Graph SDK design and system integration guide.
* [Deployment](doc/en-us/deployment/overview.md): Chat2Graph system operations guide.

## Contributing

Chat2Graph currently only provides basic agent system capabilities. We look forward to your participation in co-building graph native agentic system.

This is our technical roadmap:

- Reasoning && Planning
  - [x] One-Active-Many-Passive hybrid multi-agent architecture.
  - [x] Dual-LLM reasoning machine combining fast & slow thinking.
  - [x] Chain of agents (CoA) oriented task decomposition and graph planner.
  - [ ] Workflow auto-generation.
  - [ ] Action recommendation in operator.
  - [ ] Structured agent role management.
  - [ ] Agent task compiler.
- Memory && Knowledge
  - [x] Hierarchical memory system.
  - [x] Vector and graph knowledge base.
  - [ ] Knowledge refinement mechanism.
  - [ ] Environment management.
- Tool && System
  - [x] Toolkit knowledge graph.
  - [ ] Toolkit graph optimizer.
  - [ ] Rich toolkit/MCP integration.
  - [ ] Unified resource manager.
  - [ ] Tracing and control capabilities.
  - [ ] Benchmark.
- Product && Ecosystem
  - [x] Concise intelligent agent SDK.
  - [x] Web Service and interaction.
  - [x] One-click configuration of agents.
  - [ ] Multimodal capabilities.
  - [ ] Production enhancement.
  - [ ] Integration with open-source ecosystems.

You can reference [Contributing][contrib] document and submit GitHub Issues/PRs to provide feedback and suggest improvements for Chat2Graph.

TuGraph establishes a clear [Architecture][arch] and [Roles][roles] for the community, and will invite outstanding contributors to join [SIGs][sigs].

## Contact

You can contact with us directly through TuGraph Discord and WeChat group provided below.

- Discord：https://discord.gg/KBCFbNFj
- WeChat：![](https://github.com/TuGraph-family/community/blob/master/assets/contacts.png)

[conda]: https://docs.conda.io/projects/conda/en/latest/user-guide/install/index.html
[contrib]: https://github.com/TuGraph-family/community/blob/master/docs/CONTRIBUTING.md
[arch]: https://github.com/TuGraph-family/community/blob/master/assets/arch.png
[roles]: https://github.com/TuGraph-family/community/blob/master/docs/ROLES.md
[sigs]: https://github.com/TuGraph-family/community/blob/master/docs/SIGS.md



