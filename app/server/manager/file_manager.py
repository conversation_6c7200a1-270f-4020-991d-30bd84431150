from typing import Any, Dict, <PERSON><PERSON>

from werkzeug.datastructures import FileStorage

from app.core.service.file_service import FileService


class FileManager:
    """File Manager class to handle business logic"""

    def __init__(self):
        self._file_service: FileService = FileService.instance

    def upload_file(self, file: FileStorage) -> Tuple[Dict[str, Any], str]:
        """Upload a file.

        Args:
            file (FileStorage): file

        Returns:
            Tuple[Dict[str, Any], str]: A tuple containing upload status and success message
        """

        file_id = self._file_service.upload_or_update_file(file)
        data = {"file_id": file_id}
        return data, "File uploaded successfully"

    def delete_file(self, id: str) -> <PERSON><PERSON>[Dict[str, Any], str]:
        """Dlete file by ID.

        Args:
            id (str): ID of the file

        Returns:
            Tuple[Dict[str, Any], str]: A tuple containing deletion status and success message
        """
        self._file_service.delete_file(id)
        data: dict = {}
        return data, "File deleted successfully"
