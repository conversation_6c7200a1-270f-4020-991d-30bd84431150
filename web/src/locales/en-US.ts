export default {
  knowledgebase: {
    home: {
      title: 'Knowledge Base Management',
      subTitle1: 'Global Knowledge Base',
      subTitle2: 'Session Knowledge Base',
      subTitle3: 'AI Consensus',
      docs: 'Document Count',
      remove: 'Clear',
      removeConfirm: "Will clear all the content of the knowledge base, affecting the output of the corresponding session, please confirm whether to clear?",
      edit: 'Edit Knowledge Base',
      name: 'Knowledge Base Name',
      description: 'Knowledge Base Description',
      nameRequired: 'Please enter the knowledge base name',
    },
    detail: {
      breadcrumb1: "Knowledge Base",
      breadcrumb2: "Knowledge Base Details",
      label1: "Created By",
      label2: "Document Name",
      label3: "File Type",
      label4: "Data Size",
      label5: "Status",
      label6: "Updated Time",
      label7: "Operations",
      addFile: "Add Local File",
      step1: "Upload Local File",
      step2: "Data Processing Configuration",
      upload: {
        title: "Click/drag files here to upload",
        description: "Supports PDF, TXT,XLSX, DOC, DOCX, MD, The file size does not exceed 20MB",
        required: "Please upload a file",
        errorSize: "File size cannot exceed 20MB",
      },
      configRequired: "Please configure the data processing parameters",
      removeFile: "The document will affect the output of the corresponding session, please confirm whether to delete?",
      success: 'Success',
      fail: 'Error',
      pending: 'Adding',
      jsonTip: 'Please enter a valid JSON format',
      local: 'Local File'

    },
    docs: 'Document Count',
  },
  database: {
    title: 'Graph Database Management',
    columns: {
      name: 'Graph Database Name',
      ip: 'IP Address',
      default: 'Default Graph Schema',
      status: 'Available Status',
      updateTime: 'Update Time',
      operation: 'Operation',
      defaultTag: 'Default',
      host: 'Graph Database Address',
    },
    deleteConfirm: 'Are you sure you want to delete the graph database {name}?',
    modal: {
      title1: 'New Graph Database',
      title2: 'Edit Graph Database',
      label0: 'Graph database name',
      placeholder0: 'Enter the graph database name',
      label1: 'Database type',
      placeholder1: 'Select the database type',
      label2: 'Host address',
      placeholder2: 'Enter the Host address',
      label3: 'Port',
      placeholder3: 'Enter the Port',
      label4: 'Username',
      placeholder4: 'Enter the username',
      label5: 'Password',
      placeholder5: 'Enter the password',
      label6: 'Default graph schema',
      placeholder6: 'Enter the default graph schema name',
      label7: "Description",
      placeholder7: 'Enter the description',
    },
  },
  actions: {
    new: 'New',
    delete: 'Delete',
    next: "Next",
    ok: "OK",
    cancel: "Cancel",
    edit: 'Edit',
    setDefault: 'Set as Default',
  },
  home: {
    collapse: 'Open Sidebar',
    expand: 'Collapse Sidebar',
    openNewConversation: 'Open New Conversation',
    newConversation: 'New Conversation',
    tips: 'Only show the last 10 conversations',
    manager: 'Go to Manager',
    model: 'Design Expert',
    exportData: 'Extraction Expert',
    query: 'Query Expert',
    placeholderPromptsTitle: 'You can ask like this',
    placeholderPromptsItems1: 'What is a graph?',
    placeholderPromptsItems2: 'How to query on a vertex with ISO/GQL ?',
    title: "Hi, I am Chat2Graph",
    description: "Welcome to talk about graph with me.",
    rename: 'Rename',
    delete: 'Delete',
    deleteConversation: 'Are you sure you want to delete?',
    deleteConversationSuccess: 'Delete Success',
    confirm: 'Confirm',
    cancel: 'Cancel',
    noResult: 'No Result',
    attachment: 'File Upload',
    placeholder: 'Please enter content',
    stop: 'Thinking Stopped',
    send: 'Send ⏎',
    thinks: {
      thinking: 'Thinking...',
      finished: 'Thinking finished',
      planning: 'Planning',
      planningDesc: 'Graph experts will be working on your instruction',
      expertPlanDesc: '{expert} will solve your problem',
      answer: 'Answer',
      minutes: 'm',
      seconds: 's',
      analyze: 'Analyze'
    },
    status: {
      created: 'Pending',
      failed: 'Failed',
      finished: 'Successful',
      running: 'Running',
      stopped: 'Stopped',
    },
    today: 'Today',
    yesterday: 'Yesterday',
    date: '{month}-{day}',
    current: '[Current]',
    expert: {
      MODELING: 'Design Expert',
      IMPORT: 'Extraction Expert',
      QUERY: 'Query Expert',
      ANALYSIS: 'Analysis Expert',
      ANSWERING: 'Q&A Expert',
    },
    tip: 'No default graph database was registered, we suggest you add a new one to access the completed capabilities.',
    click: 'Click here',
    recover: 'Recover'
  },
  manager: {
    return: 'Return Session'
  }
}

