.knowledge-base-total {
  padding: 20px;
  background-color: rgb(222 216 255 / 30%);
  border-radius: 8px;
  margin: 24px 0;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-title {
      font-size: 20px;
      line-height: 24px;
      display: flex;
      align-items: center;
    }

    &-count {
      font-size: 14px;
      margin-left: 12px;
      display: inline-block;
      padding: 0 8px;
      background-color: rgb(215 195 255 / 40%);
      border-radius: 12px;
      color: #1f0651;
    }
  }

  &-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    line-height: 24px;

    &-name {
      color: #999;
    }

    &-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 14px;

      &-desc {
        color: #999;
      }
    }
  }
}

.title {
  font-size: 24px;
  line-height: 32px;
}