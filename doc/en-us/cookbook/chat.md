---
title: Chat
---

## 1. Interface Overview

The main interface is divided into three primary sections:

* **Left Sidebar:** Contains conversation management options and history.
* **Main Chat Area:** Displays the ongoing conversation and the status of your requests.
* **Input Area:** Where you type your instructions and select the expert mode.

    ![](../../asset/image/chat-planning.png)

* **Graph Display:** During the process of operating the graph database with Chat2Graph, it can even render the involved graph, providing a very good visualization feature.

    ![](../../asset/image/chat-graph.png)

## 2. How to Use

1. **Start a Conversation:** Click "+ Open New Conversation" or select an existing one from the history.
2. **Select Expert Mode:** Choose the expert (Design, Extraction, or Query) best suited for your task (supported next release version).
3. **Enter Your Request:** Type your instruction or question into the "Please enter content" field. Be as clear and specific as possible.
4. **(Optional) Add Attachments:** If needed, click the paperclip icon to upload relevant files. for example：[<PERSON> and Juliet.txt][file].
5. **Submit:** Click the Send button.
6. **Monitor Progress:** Observe the status updates (Running, Planning, Analyze, etc.) in the main chat area.
7. **Review Results:** Once processing is complete, the results will be displayed in the main chat area. You can then ask follow-up questions or start a new request.

[file]: https://github.com/TuGraph-family/chat2graph/blob/master/doc/asset/data/Romeo%20and%20Juliet.txt