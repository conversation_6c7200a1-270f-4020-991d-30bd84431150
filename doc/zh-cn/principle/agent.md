---
title: 智能体
---

## 1. 介绍

智能体（Agent）模块是 Chat2Graph 的核心执行单元，负责接收任务（`Job`）、执行具体操作并返回结果。在多智能体系统中，智能体承担着任务处理的核心责任，通过其内置的工作流机制来协调各个组件的协同工作。

每个智能体都由角色（`Profile`）、推理器（`Reasoner`）和工作流（`Workflow`）三个核心组件构成。`Profile` 定义了智能体的身份和能力范围，`Reasoner` 提供了基于大语言模型的推理机（自然语言处理能力），而 `Workflow` 则编排了任务执行的具体流程。

```python
@dataclass
class AgentConfig:
    profile: Profile
    reasoner: Reasoner
    workflow: Workflow
```

Chat2Graph 系统中存在两类特殊的智能体：Leader 和 Expert，

- Leader 智能体专门负责任务分解和协调调度，当接收到复杂任务时，它会将任务分解为多个子任务，并根据每个子任务的特点分配给相应的 Expert 执行。
- Expert 智能体则专注于特定领域的任务处理，每个 Expert 都具有明确的专业能力边界和执行职责。

## 2. 设计

### 2.1. 角色

智能体角色（Profile）是智能体的身份标识和能力定义，包含智能体的**名称**和**描述**：

- 名称：在 Expert 中，角色名称是被 Leader 识别的标识，用于任务分配和结果追溯，比如查询某个 Expert 被分配了什么 Job。
- 描述：详细说明了智能体的专业能力、任务范围和操作限制，帮助 Leader 能够更好地根据专家配置信息来分配任务。

智能体角色的描述部分应要求具备明确性和专业性。在定义时，必须明确说明其能够处理的任务类型、执行所需的前置条件以及明确不应承担的职责范围。我们建议开发者**遵循以下指导原则，更有效地配置智能体**：

- **能力边界明确定义**：Profile 的描述部分应明确列出智能体的核心能力和操作限制。例如，Design Expert 的描述中明确说明"他只能为某个特定的图数据库实例创建或修改数据结构（Schema）"和"他本身不处理具体数据（CRUD）"。这种明确的边界定义避免了任务分配时的歧义。

- **前置条件和依赖关系声明**：对于有特定前置要求的智能体，必须在 Profile 中明确声明。如 Extraction Expert 要求"图 schema 必须已经存在且已经定义了节点标签和边标签"，这确保了 Leader 在分配任务时能够正确评估任务的可执行性。

- **职责范围的精确表述**：使用否定描述来明确智能体不应承担的职责。例如，"绝不回答关于图数据库产品或技术本身的一般性介绍或咨询"这样的表述，帮助 Leader 避免将不合适的任务分配给该智能体。

- **输出预期的具体化**：Profile 应说明智能体的典型输出形式，如"他会输出数据导入过程的总结或状态报告"，这有助于后续工作流的设计和任务链的构建。通过这样的配置指导，开发者能够创建具有清晰职责边界和明确能力定位的智能体，确保多智能体系统的高效协作。

### 2.2. 推理机

[推理机](./reasoner.md)（Reasoner）是智能体的大模型推理核心，调用大语言模型并执行推理任务。在智能体的工作流执行过程中，推理机承担着理解任务指令、生成响应内容、调用外部工具以及进行复杂推理等职责。

### 2.3. 工作流

[工作流](./workflow.md)（Workflow）是智能体执行任务的核心机制，它定义了从任务接收到结果输出的完整处理流程。工作流由一系列算子（Operator）组成，每个算子负责特定阶段的任务处理。算子之间通过 WorkflowMessage 进行数据传递，形成了一个有序的处理链路。

智能体通过工作流执行任务时，会创建相应的 Job 对象，并且 `JobService` 会跟踪任务状态。Job 对象包含了任务的目标、上下文信息以及执行过程中产生的各种中间结果。

## 3. 实现

### 3.1. Leader

[Leader](./leader.md) 智能体是智能体系统中的协调者，负责对接收到的复杂任务进行分析、分解和调度。它的主要职责包括：

- **任务规划**：将复杂任务拆解为多个可执行的子任务。
- **任务分配**：根据子任务的特点和所需资源，合理分配给相应的 Expert 智能体。
- **任务执行**：确保子任务的执行，跟踪各个子任务的执行进度，及时调整调度策略。

### 3.2. Expert

Expert 智能体是智能体系统中的专业执行者，负责处理特定领域的任务。每个专家都具有明确的专业能力边界和执行职责，能够高效地完成分配给它的子任务。

除了预定义的 Expert 配置，Chat2Graph 在未来将会支持 **Expert 自动生成功能**。Leader 能够根据任务需求自动分析所需的专业能力，并动态创建具有相应 Profile 和 Workflow 的动态 Expert 智能体。这种动态扩展能力使得系统能够应对预设配置之外的新型任务场景，通过智能化的 Agent 生成和配置过程，实现真正意义上的可扩展多智能体协作网络，甚至多智能体社群。

## 4. API

智能体的核心API设计如下：

| 方法签名 | 描述 |
|:---------|:-----|
| `get_id() -> str` | 获取智能体的唯一标识符 |
| `get_profile() -> Profile` | 获取智能体的配置信息 |
| `execute(agent_message: AgentMessage, retry_count: int = 0) -> Any` | 执行智能体任务的抽象方法，由子类实现 |
| `save_output_agent_message(job: Job, workflow_message: WorkflowMessage, lesson: Optional[str] = None) -> AgentMessage` | 保存智能体执行结果为消息对象，并持久化 |

注：智能体的执行过程采用消息驱动模式，通过 AgentMessage 接收任务请求，执行完成后生成包含结果和元数据的 WorkflowMessage。系统还集成了工件（Artifact）管理机制，用于处理执行过程中产生的各种数据资源，包括图数据、文本内容等。
