---
title: Chat2Graph 简介
---

## 1. Graph + AI 背景

传统的基于表格的数据处理技术，如分布式数据库、数据仓库、数据湖等，一直在持续演进并逐步走向成熟。相比而言，基于图的数据处理技术（图数据库、图计算引擎）提供了新型的思路和方法的同时，也面临着生态成熟度低、产品使用门槛高等问题。随着大语言模型的兴起，如何有效地将人工智能技术与图计算技术相结合（Graph + AI），将是非常值得探索的方向。一方面我们可以借助大模型、 智能体等前沿技术降低图计算产品的使用门槛，提升用户的用图体验。另一方面，图计算技术可以充分发挥图数据结构在关联性分析场景上的性能与可解释性优势，协助大模型、智能体提升推理能力以及生成质量。

## 2. Chat2Graph 是什么？

Chat2Graph 是一个图原生（Graph Native）的智能体系统。通过利用图数据结构的关系建模、可解释性等天然优势，对智能体的推理、规划、记忆、工具等关键能力进行增强，实现了图数据库的智能研发、运维、问答、生成等多样化能力，帮助用户、开发者、产品经理、解决方案架构师、运维工程师等高效使用图数据库，降低用图门槛，加速内容生成，实现与图对话。真正做到图计算技术与人工智能技术的深度融合，即「图智互融」。

![](../asset/image/graph-ai.png)

详细了解 Chat2Graph 的设计和实现细节，请参考「[技术原理](principle/overview.md)」。

## 3. 应用场景

Chat2Graph当前适用的场景有：

- **知识图谱构建与交互探索**: 从海量文本、报告或各类数据源中自动构建领域知识图谱，并允许用户通过自然语言进行查询、分析与可视化。
- **复杂关系网络的深度分析**: 在社交网络分析、金融风控（如反欺诈、关联交易识别）、供应链优化、生物信息学（如蛋白质相互作用网络分析）、情报分析等领域，揭示实体间的深层联系、关键节点和社区结构。
- **智能问答与决策支持**: 基于动态构建和更新的知识图谱，提供上下文感知的高级问答服务，为复杂决策提供数据驱动的洞察。
- **高质量推理与内容生成**: 深度结合符号主义优势，提供高质量的任务规划和记忆召回，实现精确工具调用。我们将持续改进这块能力。

参考「[快速开始](quickstart.md)」开始体验 Chat2Graph。 同时，「[使用手册](cookbook/overview.md)」详细介绍了 Chat2Graph 产品功能设计与操作方式。