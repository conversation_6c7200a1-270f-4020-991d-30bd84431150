🌐️ 中文 | [English](../../README.md)

<p align="center">
  <img src="../asset/image/head.png" width=800/>
</p>

[![Star](https://shields.io/github/stars/tugraph-family/chat2graph?logo=startrek&label=Star&color=yellow)](https://github.com/TuGraph-family/chat2graph/stargazers)
[![Fork](https://shields.io/github/forks/tugraph-family/chat2graph?logo=forgejo&label=Fork&color=orange)](https://github.com/TuGraph-family/chat2graph/forks)
[![Contributor](https://shields.io/github/contributors/tugraph-family/chat2graph?logo=actigraph&label=Contributor&color=abcdef)](https://github.com/TuGraph-family/chat2graph/contributors)
[![Commit](https://badgen.net/github/last-commit/tugraph-family/chat2graph/master?icon=git&label=Commit)](https://github.com/TuGraph-family/chat2graph/commits/master)
[![License](https://shields.io/github/license/tugraph-family/chat2graph?logo=apache&label=License&color=blue)](https://www.apache.org/licenses/LICENSE-2.0.html)
[![Release](https://shields.io/github/v/release/tugraph-family/chat2graph.svg?logo=stackblitz&label=Version&color=red)](https://github.com/TuGraph-family/chat2graph/releases)


[Chat2Graph](https://chat2graph.vercel.app) 是一个**图原生的智能体系统**（Graph Native Agentic System）。

<video controls src="https://github.com/user-attachments/assets/a9680f87-16d2-47b6-843c-7659c7f4a8a2" style="max-width: 100%;">
  您的浏览器不支持 video 标签。
</video>

以智能体为蓝图，探索「图智互融」技术创新，是我们的价值主张。


## 文档地图

* [产品简介](introduction.md)：了解 Chat2Graph 产品技术背景。
* [快速开始](quickstart.md)：从源码安装体验 Chat2Graph。
* [技术原理](principle/overview.md)：介绍 Chat2Graph 架构设计与实现原理。
* [使用手册](cookbook/overview.md)：介绍 Chat2Graph 产品功能与操作。
* [开发指南](development/overview.md)：Chat2Graph SDK 设计与系统集成指南。
* [运维手册](deployment/overview.md)：Chat2Graph 系统运维指南。

## 参与贡献

Chat2Graph 当前仅提供了基础的智能体系统能力，我们非常期待您的加入，共建图原生智能体。

这是我们的技术路线图：

- 推理 && 规划
  - [x] 单主动多被动混合多智能体架构。
  - [x] 快&慢思考结合的双LLM推理机。
  - [x] 面向智能体链（CoA）的任务分解与图规划器。
  - [ ] 工作流自动生成。
  - [ ] 算子动作推荐。
  - [ ] 结构化智能体角色管理。
  - [ ] 智能体任务编译器。
- 记忆 && 知识
  - [x] 分层记忆系统。
  - [x] 向量与图谱知识库。
  - [ ] 知识精炼机制。
  - [ ] 环境管理。
- 工具 && 系统
  - [x] 工具知识图谱。
  - [ ] 工具图谱优化器。
  - [ ] 丰富的工具集与MCP集成。
  - [ ] 统一资源管理器。
  - [ ] 跟踪与管控能力。
  - [ ] Benchmark测试。
- 产品 && 生态
  - [x] 简洁的智能体SDK。
  - [x] Web服务化与交互。
  - [x] 智能体一键配置。
  - [ ] 多模态能力。
  - [ ] 产品化增强。
  - [ ] 开源生态集成。

您可以参考[贡献文档][contrib]，提交 GitHub Issue/PR 提供反馈建议对 Chat2Graph 继续改进。
TuGraph 为社区制定了清晰的[架构][arch]和[角色][roles]，并会邀请优秀贡献者加入[特别兴趣小组][sigs]。

## 联系我们
您可以通过下面提供的 TuGraph 微信群或 Discord 与我们直接联系。

- 微信：![](https://github.com/TuGraph-family/community/blob/master/assets/contacts-cn.png)
- Discord：https://discord.gg/KBCFbNFj

[contrib]: https://github.com/TuGraph-family/community/blob/master/docs/CONTRIBUTING-cn.md
[arch]: https://github.com/TuGraph-family/community/blob/master/assets/arch.png
[roles]: https://github.com/TuGraph-family/community/blob/master/docs/ROLES-cn.md
[sigs]: https://github.com/TuGraph-family/community/blob/master/docs/SIGS-cn.md