{"name": "frontend", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "prebuild": "node script/copy_doc.js", "dev": "next dev --turbopack", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@theguild/remark-mermaid": "^0.3.0", "fumadocs-core": "15.1.1", "fumadocs-mdx": "11.5.7", "fumadocs-ui": "15.1.1", "lucide-react": "^0.513.0", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.14", "@types/mdx": "^2.0.13", "@types/node": "22.13.10", "@types/react": "^19.0.11", "@types/react-dom": "^19.0.4", "postcss": "^8.5.3", "tailwindcss": "^4.0.14", "typescript": "^5.8.2"}}