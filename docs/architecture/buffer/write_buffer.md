# WriteBuffer Architecture

## Overview

The WriteBuffer serves as the **write path abstraction layer** in the MemFuse Buffer system, implementing the Write Combining Buffer pattern from computer caching architectures. It encapsulates the entire write pipeline from message ingestion to persistence, providing a clean interface while managing complex internal components.

## Architecture Design

### Write Combining Buffer Correspondence

The WriteBuffer implements computer caching principles through a coordinated pipeline:

```mermaid
graph TB
    subgraph "Computer Write Combining Buffer"
        A1[CPU Write Requests] --> B1[Address Matching]
        B1 --> C1[Data Combining]
        C1 --> D1[Burst Write to Memory]
    end

    subgraph "MemFuse WriteBuffer Architecture"
        A2[Message Write Requests] --> B2[RoundBuffer Token Check]
        B2 --> C2[Message Accumulation]
        C2 --> D2[Batch Transfer to HybridBuffer]
    end

    subgraph "Correspondence Mapping"
        E[CPU Writes ↔ Message Writes]
        F[Address Matching ↔ Session Grouping]
        G[Data Combining ↔ Message Accumulation]
        H[Memory Burst ↔ Batch Processing]
    end
```

### WriteBuffer Abstraction Layer

```mermaid
graph TB
    subgraph "WriteBuffer Abstraction Layer"
        A[Client Messages] --> B["WriteBuffer.add()"]
        B --> C[Component Coordination]
    end

    subgraph "Internal Component Management"
        C --> D[RoundBuffer<br/>Short-term Accumulation]
        C --> E[HybridBuffer<br/>Mid-term Processing]
        C --> F[FlushManager<br/>Persistence Coordination]

        D -- "Auto Transfer<br/>(Token/Size Threshold)" --> E
        E -- "Batch Flush<br/>(Queue Full)" --> F
        F -- "Persist<br/>(MemoryService)" --> G[PostgreSQL + pgai]
    end

    subgraph "Abstraction Benefits"
        H["✅ Single Responsibility<br/>Write path only"]
        I["✅ Component Isolation<br/>Internal management"]
        J["✅ Clean Interface<br/>Simple API"]
        K["✅ Statistics<br/>Comprehensive metrics"]
    end

    classDef abstraction_node fill:#E8F5E9,stroke:#66BB6A;
    classDef component_node fill:#FFF3E0,stroke:#FFA726;
    classDef benefit_node fill:#E3F2FD,stroke:#42A5F5;

    class A,B,C abstraction_node;
    class D,E,F,G component_node;
    class H,I,J,K benefit_node;
```

## Core Components

### 1. RoundBuffer - Token-based FIFO

**Purpose**: Short-term message accumulation with intelligent transfer triggers

**Key Features**:
- **Token Counting**: Uses configurable model for accurate token calculation
- **Dual Thresholds**: Token limit (800) and size limit (5 rounds)
- **Session Tracking**: Groups messages by session context
- **Auto Transfer**: Automatic transfer when thresholds are reached

```mermaid
graph LR
    subgraph "RoundBuffer Flow"
        A[MessageList Input] --> B[Token Counting]
        B --> C{Token Limit<br/>Exceeded?}
        C -->|No| D[Add to Buffer]
        C -->|Yes| E[Transfer Trigger]
        
        D --> F[Accumulate Messages]
        E --> G[Transfer to HybridBuffer]
        G --> H[Clear Buffer]
        
        F --> I{Size Limit<br/>Exceeded?}
        I -->|Yes| E
        I -->|No| F
    end
```

**Configuration**:
```yaml
round_buffer:
  max_tokens: 800               # Token threshold for transfer
  max_size: 5                   # Maximum number of rounds
  token_model: "gpt-4o-mini"    # Model for token counting
```

### 2. HybridBuffer - Dual-Queue Storage

**Purpose**: Mid-term processing with immediate embedding calculation and dual-queue management

**Key Features**:
- **Immediate Processing**: Chunks and embeddings calculated on arrival
- **VectorCache**: Pre-calculated embeddings for instant retrieval
- **RoundQueue**: Original rounds for database persistence
- **Batch Efficiency**: Triggers flush when queue reaches capacity

```mermaid
graph TB
    subgraph "HybridBuffer Architecture"
        A[RoundBuffer Transfer] --> B[Immediate Processing]
        B --> C[Chunking & Embedding]
        B --> D[Queue Management]

        C --> E[VectorCache]
        D --> F[RoundQueue]

        E --> G[Chunks + Embeddings<br/>Ready for Retrieval]
        F --> H[Original Rounds<br/>Ready for Database]

        G --> I{Queue Size<br/>≥ max_size?}
        H --> I
        I -->|Yes| J[Batch Write to Storage]
        I -->|No| K[Keep in Memory]

        J --> L[Clear All Queues]
    end
```

**Configuration**:
```yaml
hybrid_buffer:
  max_size: 5                   # FIFO buffer size
  chunk_strategy: "message"     # Chunking strategy
  embedding_model: "all-MiniLM-L6-v2"  # Embedding model
```

### 3. FlushManager - Persistence Coordination

**Purpose**: Manages batch persistence operations with error handling and retry logic

**Key Features**:
- **Async Processing**: Non-blocking persistence operations
- **Worker Pool**: Configurable number of concurrent workers
- **Retry Logic**: Automatic retry with exponential backoff
- **Error Handling**: Comprehensive error recovery mechanisms

**Configuration**:
```yaml
performance:
  max_flush_workers: 3          # Concurrent workers
  max_flush_queue_size: 100     # Queue capacity
  flush_timeout: 30.0           # Operation timeout
  flush_strategy: "hybrid"      # Strategy selection
```

## WriteBuffer Interface

### Primary Methods

```python
class WriteBuffer:
    # Core write operations
    async def add(self, messages: MessageList, session_id: Optional[str] = None) -> Dict[str, Any]
    
    # Component access (controlled)
    def get_round_buffer(self) -> RoundBuffer
    def get_hybrid_buffer(self) -> HybridBuffer
    def get_flush_manager(self) -> FlushManager
    
    # Management operations
    async def flush_all(self) -> Dict[str, Any]
    def get_stats(self) -> Dict[str, Any]
```

### Usage Example

```python
# Initialize WriteBuffer (managed by BufferService)
write_buffer = buffer_service.get_write_buffer()

# Add messages (automatic threshold management)
result = await write_buffer.add(message_list, session_id="user_session")

# Manual flush if needed
flush_result = await write_buffer.flush_all()

# Get statistics
stats = write_buffer.get_stats()
print(f"Total writes: {stats['total_writes']}")
print(f"Transfer rate: {stats['transfer_rate']}")
```

## Data Flow Patterns

### Normal Operation Flow

```mermaid
sequenceDiagram
    participant Client
    participant WriteBuffer
    participant RoundBuffer
    participant HybridBuffer
    participant FlushManager
    participant MemoryService

    Client->>WriteBuffer: add(MessageList)
    WriteBuffer->>RoundBuffer: accumulate(messages)
    
    RoundBuffer->>RoundBuffer: check_thresholds()
    alt Threshold reached
        RoundBuffer->>HybridBuffer: transfer_batch(rounds)
        HybridBuffer->>HybridBuffer: immediate_processing()
        HybridBuffer->>HybridBuffer: update_queues()
        
        alt Queue full
            HybridBuffer->>FlushManager: batch_flush()
            FlushManager->>MemoryService: persist_data()
            MemoryService-->>FlushManager: success
            FlushManager-->>HybridBuffer: complete
            HybridBuffer->>HybridBuffer: clear_queues()
        end
    end
    
    WriteBuffer-->>Client: success_response
```

## Performance Characteristics

### Throughput Optimization

| Metric | Value | Description |
|--------|-------|-------------|
| **Message Add** | <5ms | Add to RoundBuffer |
| **Buffer Transfer** | <50ms | RoundBuffer → HybridBuffer |
| **Storage Flush** | <200ms | HybridBuffer → Persistent Storage |
| **Batch Size** | 5 rounds | Optimal batch size for efficiency |

### Memory Management

```mermaid
graph LR
    subgraph "Memory Usage Pattern"
        A[RoundBuffer<br/>~1MB] --> B[HybridBuffer<br/>~5MB]
        B --> C[FlushManager<br/>~1MB]
        C --> D[Total Memory<br/>~7MB]
    end
    
    subgraph "Garbage Collection"
        E[FIFO Eviction] --> F[Automatic Cleanup]
        F --> G[Memory Efficiency]
    end
```

## Error Handling & Resilience

### Fault Tolerance Strategy

```mermaid
graph TB
    subgraph "Error Handling"
        A[Component Failure] --> B{Failure Type}
        B -->|Storage Error| C[Retry with Backoff]
        B -->|Memory Error| D[Graceful Degradation]
        B -->|Timeout Error| E[Circuit Breaker]
        
        C --> F[Automatic Recovery]
        D --> G[Fallback Mode]
        E --> H[Service Protection]
    end
```

### Recovery Mechanisms

1. **Automatic Retry**: Transient failure recovery with exponential backoff
2. **Circuit Breaker**: Prevents cascade failures during persistent issues
3. **Graceful Degradation**: Continues operation with reduced functionality
4. **Data Persistence**: Ensures no data loss during component failures

## Monitoring & Metrics

### Key Metrics

```python
# WriteBuffer Statistics
{
    "total_writes": 1250,
    "total_transfers": 45,
    "transfer_rate": 3.6,  # transfers per minute
    "average_batch_size": 27.8,
    "component_health": {
        "round_buffer": "healthy",
        "hybrid_buffer": "healthy", 
        "flush_manager": "healthy"
    },
    "performance": {
        "avg_write_latency": 4.2,  # ms
        "avg_transfer_latency": 48.5,  # ms
        "memory_usage": 6.8  # MB
    }
}
```

### Health Indicators

| Metric | Healthy Range | Alert Threshold |
|--------|---------------|-----------------|
| Transfer Rate | 1-10/min | >20/min |
| Write Latency | <10ms | >50ms |
| Memory Usage | <10MB | >20MB |
| Error Rate | <1% | >5% |

## Configuration Best Practices

### Production Settings

```yaml
buffer:
  enabled: true
  round_buffer:
    max_tokens: 800      # Balanced for throughput
    max_size: 5          # Optimal batch size
  hybrid_buffer:
    max_size: 5          # Memory efficiency
  performance:
    max_flush_workers: 3 # Concurrent processing
    flush_timeout: 30.0  # Reasonable timeout
```

### Development Settings

```yaml
buffer:
  enabled: false  # Use bypass mode for development
```

## Related Documentation

- **[Components](components.md)** - Detailed component implementation
- **[Configuration](configuration.md)** - Complete configuration guide
- **[Performance](performance.md)** - Performance analysis and tuning
- **[Overview](overview.md)** - Buffer system overview
