# QueryBuffer Architecture

## Overview

The QueryBuffer implements the **Multi-level Cache Hierarchy** pattern from computer caching architectures, serving as the query path abstraction layer in the MemFuse Buffer system. It provides intelligent caching, multi-source coordination, and result aggregation for optimal query performance.

## Architecture Design

### Multi-level Cache Hierarchy Correspondence

```mermaid
graph TB
    subgraph "Computer Multi-level Cache Hierarchy"
        A1[Query Request] --> B1[Multi-level Cache Lookup]
        B1 --> C1[L1/L2/L3 Cache Check]
        C1 --> D1[Backend Storage Query]
        D1 --> E1[Result Aggregation]
    end

    subgraph "MemFuse QueryBuffer Architecture"
        A2[Query Request] --> B2[Cache Key Check]
        B2 --> C2[HybridBuffer + Storage Query]
        C2 --> D2[Multi-source Retrieval]
        D2 --> E2[Result Combination]
    end

    subgraph "Correspondence Analysis"
        F["Multi-level Cache to Cache + Buffer + Storage"]
        G["L1/L2/L3 Hierarchy to Memory Hierarchy"]
        H["Backend Storage to MemoryService"]
        I["Result Aggregation to Result Combination"]
    end
```

## Core Architecture

### QueryBuffer Workflow

```mermaid
graph TD
    Query["User Query"] --> Cache{"Query Cache (LRU)<br/>L1 Level: Result Cache"}

    Cache -->|"Hit"| Result["Final Results"]

    subgraph miss_sub ["On Cache Miss (Parallel Query)"]
        Cache -->|"Miss"| Fork[" "]
        Fork -->|"Query Hot Memory Data (L2 Level)"| HB["HybridBuffer"]
        Fork -->|"Query Persistent Data (L3 Level)"| MS["MemoryService"]

        HB --> Merger["Result Aggregation & Ranking<br/>Aggregate & Rank"]
        MS --> Merger
    end

    Merger -->|"Return & Populate Cache"| Cache
    Merger --> Result

    classDef l1_cache fill:#E8F5E9,stroke:#66BB6A
    classDef l2_cache fill:#FFF3E0,stroke:#FFA726
    classDef l3_cache fill:#FBE9E7,stroke:#FF5722
    classDef query_node fill:#E3F2FD,stroke:#42A5F5
    classDef process_node fill:#F5F5F5,stroke:#9E9E9E

    class Query,Result query_node
    class Cache l1_cache
    class HB l2_cache
    class MS l3_cache
    class Merger process_node
    class Fork process_node
```

## Core Components

### 1. Query Cache (LRU)

**Purpose**: L1-level cache for previously computed query results

**Key Features**:
- **LRU Eviction**: Least Recently Used eviction policy
- **Query Similarity**: Intelligent query matching and result reuse
- **Configurable Size**: Adjustable cache capacity
- **TTL Support**: Time-to-live for cache entries

```python
# Cache Configuration
query_cache_config = {
    "max_size": 100,        # Maximum cached queries
    "cache_ttl": 300,       # Time-to-live in seconds
    "similarity_threshold": 0.95  # Query similarity threshold
}
```

### 2. Multi-source Coordinator

**Purpose**: Orchestrates parallel queries across multiple data sources

**Data Sources**:
1. **HybridBuffer**: Recent data in memory (L2-level)
2. **MemoryService**: Persistent storage access (L3-level)
3. **SpeculativeBuffer**: Prefetched data (Future implementation)

```mermaid
graph TB
    subgraph "Multi-source Query Coordination"
        A[Query Request] --> B[Source Selection]
        B --> C[Parallel Query Execution]
        
        C --> D[HybridBuffer Query<br/>Hot Memory Data]
        C --> E[MemoryService Query<br/>Persistent Data]
        C --> F["SpeculativeBuffer Query<br/>Prefetched Data Future"]
        
        D --> G[Result Aggregation]
        E --> G
        F --> G
        
        G --> H[Deduplication & Ranking]
        H --> I[Final Results]
    end
```

### 3. Result Aggregator

**Purpose**: Combines and ranks results from multiple sources

**Key Features**:
- **Deduplication**: Removes duplicate results across sources
- **Relevance Scoring**: Combines scores from different sources
- **Result Ranking**: Sorts by relevance, timestamp, or custom criteria
- **Source Attribution**: Tracks result origins for debugging

## QueryBuffer Interface

### Primary Methods

```python
class QueryBuffer:
    # Core query operations
    async def query(
        self, 
        query_text: str, 
        top_k: int = 10,
        sort_by: str = "score",
        order: str = "desc",
        use_rerank: bool = True
    ) -> List[Dict[str, Any]]
    
    # Session-based queries
    async def query_by_session(
        self,
        session_id: str,
        limit: int = 10,
        sort_by: str = "timestamp",
        order: str = "desc"
    ) -> List[Dict[str, Any]]
    
    # Cache management
    def clear_cache(self) -> None
    def get_cache_stats(self) -> Dict[str, Any]
    
    # Statistics and monitoring
    def get_stats(self) -> Dict[str, Any]
```

### Usage Examples

```python
# Basic query with caching
results = await query_buffer.query(
    query_text="machine learning concepts",
    top_k=5,
    sort_by="score"
)

# Session-specific query
session_results = await query_buffer.query_by_session(
    session_id="user_123",
    limit=10,
    sort_by="timestamp"
)

# Cache statistics
cache_stats = query_buffer.get_cache_stats()
print(f"Cache hit rate: {cache_stats['hit_rate']:.2%}")
```

## Query Processing Pipeline

### Detailed Query Flow

```mermaid
sequenceDiagram
    participant Client
    participant QueryBuffer
    participant Cache
    participant HybridBuffer
    participant MemoryService
    participant Aggregator

    Client->>QueryBuffer: query(text, top_k)
    QueryBuffer->>Cache: check_cache(query_hash)
    
    alt Cache Hit
        Cache-->>QueryBuffer: cached_results
        QueryBuffer-->>Client: return_cached_results
    else Cache Miss
        QueryBuffer->>QueryBuffer: initiate_parallel_query()
        
        par Parallel Query Execution
            QueryBuffer->>HybridBuffer: query_buffer(text, top_k)
            QueryBuffer->>MemoryService: query_storage(text, top_k)
        end
        
        HybridBuffer-->>Aggregator: buffer_results
        MemoryService-->>Aggregator: storage_results
        
        Aggregator->>Aggregator: deduplicate_and_rank()
        Aggregator-->>QueryBuffer: final_results
        
        QueryBuffer->>Cache: update_cache(query, results)
        QueryBuffer-->>Client: return_final_results
    end
```

## Performance Characteristics

### Query Performance Metrics

| Operation | Latency | Description |
|-----------|---------|-------------|
| **Cache Hit** | <10ms | Direct cache response |
| **Buffer Query** | <50ms | HybridBuffer search |
| **Storage Query** | <100ms | MemoryService search |
| **Cold Query** | <150ms | Multi-source aggregation |

### Cache Performance

```mermaid
graph LR
    subgraph "Cache Performance"
        A["Cache Hit Rate<br/>Target: >80%"] --> B["Query Latency<br/>Target: <50ms"]
        B --> C["Memory Usage<br/>Target: <2MB"]
        C --> D["Overall Performance<br/>Optimized"]
    end
```

## Configuration

### QueryBuffer Configuration

```yaml
query:
  max_size: 15                  # Maximum results per query
  cache_size: 100               # Query cache size
  default_sort_by: "score"      # Default sorting method
  default_order: "desc"         # Default sort order
  cache_ttl: 300               # Cache time-to-live (seconds)
  similarity_threshold: 0.95    # Query similarity threshold
```

### Sorting Options

**Available Sort Fields**:
- `score`: Relevance-based ranking (default)
- `timestamp`: Temporal ordering
- `session_id`: Session-based grouping

**Sort Orders**:
- `desc`: Descending order (default)
- `asc`: Ascending order

## Advanced Features

### Query Similarity Detection

```python
# Query similarity examples
queries = [
    "machine learning algorithms",
    "ML algorithms",              # Similar to first
    "deep learning networks",     # Different topic
]

# QueryBuffer automatically detects similar queries
# and reuses cached results when similarity > threshold
```

### Result Reranking

When `use_rerank=True`, QueryBuffer applies additional ranking:

1. **Semantic Reranking**: Uses advanced models for better relevance
2. **Temporal Boosting**: Recent results get slight score boost
3. **Source Weighting**: Different sources have different weights

### Multi-source Result Fusion

```python
# Result fusion strategy
fusion_weights = {
    "hybrid_buffer": 0.4,    # Recent data weight
    "memory_service": 0.6,   # Persistent data weight
    "speculative": 0.2       # Prefetched data weight (future)
}
```

## Monitoring & Metrics

### Key Performance Indicators

```python
# QueryBuffer Statistics
{
    "total_queries": 2500,
    "cache_hits": 2000,
    "cache_misses": 500,
    "hit_rate": 0.80,           # 80% cache hit rate
    "avg_query_latency": 45.2,  # ms
    "avg_cache_latency": 8.5,   # ms
    "avg_cold_latency": 125.3,  # ms
    "source_distribution": {
        "cache": 0.80,
        "hybrid_buffer": 0.15,
        "memory_service": 0.05
    },
    "memory_usage": 1.8         # MB
}
```

### Health Monitoring

| Metric | Healthy Range | Alert Threshold |
|--------|---------------|-----------------|
| Cache Hit Rate | >70% | <50% |
| Query Latency | <100ms | >500ms |
| Memory Usage | <5MB | >10MB |
| Error Rate | <2% | >10% |

## Error Handling

### Fault Tolerance

```mermaid
graph TB
    subgraph "Error Handling Strategy"
        A[Query Failure] --> B{Failure Type}
        B -->|Cache Error| C[Bypass Cache]
        B -->|Source Error| D[Use Available Sources]
        B -->|Network Error| E[Retry with Backoff]
        
        C --> F[Direct Source Query]
        D --> G[Partial Results]
        E --> H[Graceful Degradation]
    end
```

### Recovery Mechanisms

1. **Cache Bypass**: Continue operation without cache on cache failures
2. **Partial Results**: Return available results when some sources fail
3. **Automatic Retry**: Retry failed operations with exponential backoff
4. **Graceful Degradation**: Reduce functionality while maintaining core operations

## Best Practices

### Query Optimization

1. **Use Specific Queries**: More specific queries have better cache hit rates
2. **Consistent Formatting**: Consistent query formatting improves cache efficiency
3. **Appropriate top_k**: Use reasonable top_k values to balance performance and completeness
4. **Session Grouping**: Use session-based queries for user-specific data

### Cache Management

1. **Monitor Hit Rates**: Maintain >70% cache hit rate for optimal performance
2. **Adjust Cache Size**: Increase cache size if memory allows and hit rate is low
3. **TTL Tuning**: Adjust TTL based on data freshness requirements
4. **Regular Cleanup**: Periodic cache cleanup prevents memory bloat

## Related Documentation

- **[Overview](overview.md)** - Buffer system overview
- **[Write Buffer](write_buffer.md)** - Write path architecture
- **[Performance](performance.md)** - Performance analysis and tuning
- **[Configuration](configuration.md)** - Complete configuration guide
