app:
  name: "Chat2Graph"
  desc: "An Agentic System on Graph Database."
  version: "0.0.1"

plugin:
  workflow_platform: "DBGPT"

reasoner:
  type: "DUAL"

tools:
  - &knowledge_base_retriever_tool
    name: "KnowledgeBaseRetriever"
    type: "LOCAL_TOOL"
    module_path: "app.core.resource.question_answering"

  - &internet_retriever_tool
    name: "InternetRetriever"
    type: "LOCAL_TOOL"
    module_path: "app.core.resource.question_answering"

  - &reference_generator_tool
    name: "ReferenceGenerator"
    type: "LOCAL_TOOL"
    module_path: "app.core.resource.question_answering"

actions:
  # question answering actions
  - &knowledge_base_retrieving_action
    name: "knowledge_base_retrieving"
    desc: "调用knowledge_base_search工具，从外接知识库中检索得到问题相关的文档"
    tools:
      - *knowledge_base_retriever_tool

  - &internet_retrieving_action
    name: "internet_retrieving"
    desc: "调用internet_search工具，从互联网搜索引擎中检索得到问题相关的文档"
    tools:
      - *internet_retriever_tool

  - &reference_listing_action
    name: "reference_listing"
    desc: "调用reference_list工具，以markdown格式返回推理过程中所涉及的原文出处链接，方便展示"
    tools:
      - *reference_generator_tool

toolkit:
  - [*knowledge_base_retrieving_action, *internet_retrieving_action]
  - [*reference_listing_action]

operators:
  # question answering operators
  - &retrieval_operator
    instruction: |
      你是一位专业的文档检索专家。你的工作是，从知识库以及互联网两个信息来源检索与问题相关的文档，
      仔细阅读检索得到的文档材料，分别总结每一份文档，为后续回答用户问题作准备。
      你阅读的文档未必与用户的问题直接相关，但是你仍然需要进行清晰全面的总结。
      你的任务是检索并总结文档，为后续推理得到最终的答案做铺垫。

      请认真理解给定的问题，同时，按要求完成任务：

      1.文档检索
      - 通过知识库检索得到与问题相关的文档
      - 通过互联网检索得到与问题相关的网页
      2. 文档整理
      - 将知识库中检索得到各个文档分别总结为一段内容
      - 将互联网中检索得到各个网页内容分别总结为一段内容
    output_schema: |
      {
          "original_question": "输入的原始提问",
          "knowledge_base_result": ["知识库中", "检索得到的", "相关内容", "总结"],
          "knowledge_base_references": ["知识库中", "检索得到的", "相关内容", "对应的", "章节编号"],
          "internet_result": ["互联网", "搜索引擎中", "检索得到的", "相关内容", "总结"],
          "internet_references": ["互联网", "搜索引擎中", "检索得到的", "相关内容", "对应的", "网址"]
      }
    actions:
      - *knowledge_base_retrieving_action
      - *internet_retrieving_action

  - &summary_operator
    instruction: |
      你是一位文档总结专家,擅长总结归纳不同来源的文档。你需要根据用户的问题，总结归纳出用户需要的答案。

      基于检索得到的文档内容,完成以下文档总结任务:

      1. 分别总结不同来源的文档内容
      - 总结从知识库中检索得到问题相关内容
      - 总结从互联网搜索引擎中检索得到问题相关内容

      2. 归纳不同来源的总结结果
      - 分析不同来源的文档总结的相同点与不同点
      - 归纳得出一份更完整的总结内容

      3. 答案生成
      - 分析问题的实际意图
      - 根据问题与归纳总结的文档内容，生成一份回答
      - 提供回答中涉及的原文出处，给出一个List，其中包含markdown格式的原文链接
    output_schema: |
      {
          "anwser": "针对用户问题的最终回答",
          "references": ["回答生成", "过程中", "参考的", "文档原文的", "markdown格式", "链接"]
      }
    actions:
      - *reference_listing_action

experts:
  - profile:
      name: "Q&A Expert"
      desc: |
        你是一位专业的文档检索专家。你的工作是，从知识库以及互联网两个信息来源检索与问题相关的文档，
        仔细阅读检索得到的文档材料，分别总结每一份文档，为后续回答用户问题作准备。
        你阅读的文档未必与用户的问题直接相关，但是你仍然需要进行清晰全面的总结。
        你的任务是检索并总结文档，为后续推理得到最终的答案做铺垫。
    reasoner:
      actor_name: "Q&A Expert"
      thinker_name: "Q&A Expert"
    workflow:
      - [*retrieval_operator, *summary_operator]

knowledgebase: {}
memory: {}
env: {}
