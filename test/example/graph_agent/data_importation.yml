app:
  name: "Chat2Graph"
  desc: "An Agentic System on Graph Database."
  version: "0.0.1"

plugin:
  workflow_platform: "DBGPT"

reasoner:
  type: "DUAL"

tools:
  - &schema_getter_tool
    name: "SchemaGetter"
    type: "LOCAL_TOOL"
    module_path: "app.core.resource.data_importation"

  - &document_reader_tool
    name: "DocumentReader"
    type: "LOCAL_TOOL"
    module_path: "app.core.resource.data_importation"

  - &data_import_tool
    name: "DataImport"
    type: "LOCAL_TOOL"
    module_path: "app.core.resource.data_importation"

actions:
  # data importation actions
  - &schema_understanding_action
    name: "schema_understanding"
    desc: "调用相关工具获取图模型，并对图模型进行分析和理解"
    tools:
      - *schema_getter_tool

  - &content_understanding_action_2
    name: "content_understanding_2"
    desc: "调用相关工具获取原始文本内容，并结合图模型进行分析和理解"
    tools:
      - *document_reader_tool

  - &triplet_data_generation_action
    name: "triplet_data_generation"
    desc: "根据图模型理解和文本内容理解，进行三元组数据抽取并存入图数据库"
    tools:
      - *data_import_tool

  - &output_result_action
    name: "output_result"
    desc: "输出数据导入结果的汇总信息"

toolkit:
  - [
      *schema_understanding_action,
      *content_understanding_action_2,
      *triplet_data_generation_action,
      *output_result_action,
    ]

operators:
  # data importation operators
  - &data_importation_operator
    instruction: |
      你是一位资深的图数据抽取专家。
      你的使命是，基于已分析的文档内容和图模型，精准地抽取关键信息，为构建知识图谱提供坚实的数据基础。
      在这一阶段，你不是在创造知识，而是在发掘隐藏在文档中的事实。
      你的目标是从文本中提取实体、关系和属性，请确保数据的准确、丰富、完整，因为后续的知识图谱构建将直接依赖于你抽取的数据质量。
      抽取数据完成后，你需要调用指定的工具，完成数据的导入。
      最后需要输出导入结果的总结。

      必须执行以下全部步骤：
      1. 调用相关工具获取图模型，并对图模型进行分析和理解
      2. 调用相关工具获取文本内容，并结合图模型进行分析和理解
      3. 根据对图模型理解和文本内容理解的结果，进行三元组数据的抽取（多次抽取），并存入图数据库中
      4. 输出数据导入结果
    output_schema: |
      {
          "result": "成功导入实体的数量、成功导入关系的数量；（如果错误，原因是什么）"
      }
    actions:
      - *schema_understanding_action
      - *content_understanding_action_2
      - *triplet_data_generation_action
      - *output_result_action

experts:
  - profile:
      name: "Extraction Expert"
      desc: |
        你是一位资深的图数据抽取专家。
        你的使命是，基于已分析的文档内容和图模型，精准地抽取关键信息，为构建知识图谱提供坚实的数据基础。
        在这一阶段，你不是在创造知识，而是在发掘隐藏在文档中的事实。
        你的目标是从文本中提取实体、关系和属性，请确保数据的准确、丰富、完整，因为后续的知识图谱构建将直接依赖于你抽取的数据质量。
        抽取数据完成后，你需要调用指定的工具，完成数据的导入。
        最后需要输出导入结果的总结。
    reasoner:
      actor_name: "Extraction Expert"
      thinker_name: "Extraction Expert"
    workflow:
      - [*data_importation_operator]

knowledgebase: {}
memory: {}
env: {}
