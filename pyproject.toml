[tool.poetry]
name = "chat2graph"
version = "0.1.0"
description = "Chat2Graph: A Graph Native Agentic System."
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
packages = [
    { include = "app" }
]

[tool.poetry.dependencies]
python = ">=3.10,<3.12"
openai = "^1.86.0"
aisuite = {extras = ["all"], version = "0.1.11"}
litellm = {extras = ["proxy"], version = "1.72.6"} # pip install "aiohttp>=3.12.13" # litellm has conflict with dbgpt which requires aiohttp = "3.8.4"
docstring-parser = "^0.16"  # used by aisuite
pydantic = "^2.11.7"
typing-inspect = ">=0.9.0"
pytest-asyncio = "^1.0.0"
chromadb = "^1.0.12"
neo4j = "^5.28.1"
sqlalchemy-utils = "^0.41.2"
matplotlib = "^3.10.3"
networkx = "^3.4.2"
pyfiglet = "^1.0.3"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "aliyun"
priority = "supplemental"
url = "https://mirrors.aliyun.com/pypi/simple/"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "8.4.0"
ruff = "0.11.13"
mypy = "1.16.0"

[tool.poetry.group.service.dependencies]
flask = "3.1.1"
flask-sqlalchemy = "3.1.1"
flask-cors = "6.0.1"

[tool.poetry.group.test.dependencies]
pytest = "8.4.0"
pytest-cov = "6.2.1"
pytest-mock = "^3.14.1"

[tool.poetry.group.db-gpt.dependencies]
dbgpt = { version = "0.7.1", extras = ["agent", "simple_framework", "framework"] }
dbgpt-ext = { version = "0.7.1", extras = ["rag", "graph_rag", "storage_chromadb"] }

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle error
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "EXE",
]
ignore = [
    "UP006",    # use List not list
    "UP035",
    "UP007",
]

[tool.ruff.lint.isort]
combine-as-imports = true
force-sort-within-sections = true
known-first-party = ["app"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.pytest.ini_options]
testpaths = ["test"]
python_files = ["test_*.py"]
addopts = "-v"
asyncio_mode = "auto"  # Enable asyncio mode
markers = [
    "asyncio: mark test as async"
]